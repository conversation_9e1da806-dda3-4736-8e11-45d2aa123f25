FROM jnbyharbor.jnby.com/base-images/baseimagejdk8:v1.3

MAINTAINER box-group

RUN mkdir -p /jic-store-inspection/jic-store-inspection-center

WORKDIR /jic-store-inspection/jic-store-inspection-center

EXPOSE 9359

COPY target/jic-store-inspection-center.jar jic-store-inspection-center.jar

ENV TZ='Asia/Shanghai'

ENTRYPOINT ["java", "-Xmx1g", "-Xms1g","-XX:NewRatio=3","-Xss512k", "-Xmn1g","-XX:SurvivorRatio=2", "-XX:MaxMetaspaceSize=192m", "-XX:MetaspaceSize=192m", "-XX:+UseParallelGC","-Dreactor.netty.pool.leasingStrategy=lifo", "-jar", "jic-store-inspection-center.jar"]

CMD ["--spring.profiles.active=test"]
