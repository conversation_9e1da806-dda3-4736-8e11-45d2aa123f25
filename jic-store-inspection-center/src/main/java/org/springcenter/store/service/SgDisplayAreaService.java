package org.springcenter.store.service;

import com.jnby.common.Page;
import org.springcenter.store.dto.DisplayAreaListReq;
import org.springcenter.store.dto.SgDisplayAreaResp;
import org.springcenter.store.entity.SgDisplayArea;

import java.util.List;

public interface SgDisplayAreaService {
    /**
     * 列表查询
     * @param requestData
     * @param page
     * @return
     */
    List<SgDisplayAreaResp> list(DisplayAreaListReq requestData, Page page);

    /**
     * 创建或者编辑
     * @param requestData
     */
    void createOrUpdate(SgDisplayAreaResp requestData);

    /**
     * 更新上下架 或者 删除  需要处理  检查表和陈列区域关系表 内的数据
     * @param requestData
     */
    void updateIsDelOrStatus(SgDisplayAreaResp requestData);


    public  List<SgDisplayAreaResp> buildSgDisplayAreaResp(List<SgDisplayArea> list);
}
