package org.springcenter.store.service;

import com.jnby.common.Page;
import org.springcenter.store.dto.ChecklistReq;
import org.springcenter.store.dto.SgChecklistResp;

import java.util.List;

public interface SgChecklistService {
    /**
     * 检查表列表
     * @param checklistReq
     * @param page
     * @return
     */
    List<SgChecklistResp> list(ChecklistReq checklistReq, Page page);

    /**
     * 新增或者编辑
     * @param requestData
     */
    void createOrUpdate(SgChecklistResp requestData);
}
