package org.springcenter.store.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.store.cons.AreaIdConstant;
import org.springcenter.store.dto.ChecklistReq;
import org.springcenter.store.dto.SgChecklistResp;
import org.springcenter.store.dto.SgDisplayAreaResp;
import org.springcenter.store.entity.SgChecklist;
import org.springcenter.store.entity.SgChecklistDisplayRelation;
import org.springcenter.store.entity.SgDisplayArea;
import org.springcenter.store.mapper.SgChecklistDisplayRelationMapper;
import org.springcenter.store.mapper.SgChecklistMapper;
import org.springcenter.store.mapper.SgDisplayAreaMapper;
import org.springcenter.store.service.SgChecklistService;
import org.springcenter.store.service.SgDisplayAreaService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SgChecklistServiceImpl implements SgChecklistService {

    @Autowired
    private SgChecklistMapper sgChecklistMapper;

    @Autowired
    private SgDisplayAreaService sgDisplayAreaService;

    @Autowired
    private SgChecklistDisplayRelationMapper sgChecklistDisplayRelationMapper;

    @Autowired
    private SgDisplayAreaMapper sgDisplayAreaMapper;




    @Override
    public List<SgChecklistResp> list(ChecklistReq checklistReq, Page page) {
        com.github.pagehelper.Page<SgChecklist> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        sgChecklistMapper.selectListByReq(checklistReq);
        PageInfo<SgChecklist> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<SgChecklist> list1 = pageInfo.getList();
        // 封装内层参数
        List<SgChecklistResp> list2 = buildSgChecklistResp(list1);
        return list2;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdate(SgChecklistResp requestData) {
        if(requestData.getId() == null){
            SgChecklist sgChecklist = new SgChecklist();
            BeanUtils.copyProperties(requestData,sgChecklist);
//            sgChecklist.setId(AreaIdConstant.sg_checklist);
            sgChecklist.setUpdateTime(new Date());
            sgChecklist.setCreateTime(new Date());
            sgChecklist.setIsDel(IsDeleteEnum.NORMAL.getCode());
            sgChecklistMapper.insert(sgChecklist);
            requestData.setId(sgChecklist.getId());
        }else{
            SgChecklist sgChecklist = new SgChecklist();
            BeanUtils.copyProperties(requestData,sgChecklist);
            sgChecklist.setUpdateTime(new Date());
            sgChecklistMapper.updateById(sgChecklist);
        }

        if(CollectionUtils.isNotEmpty(requestData.getSgDisplayAreaRespList())){
            // 处理数据  删除之前的
            QueryWrapper<SgChecklistDisplayRelation> queryWrapper = new QueryWrapper<>();
            SgChecklistDisplayRelation sgChecklistDisplayRelation = new SgChecklistDisplayRelation();
            sgChecklistDisplayRelation.setSgChecklistId(requestData.getId());
            queryWrapper.setEntity(sgChecklistDisplayRelation);
            sgChecklistDisplayRelationMapper.delete(queryWrapper);
            // 新增数据
            for (SgDisplayAreaResp sgDisplayAreaResp : requestData.getSgDisplayAreaRespList()) {
                SgChecklistDisplayRelation sgChecklistDisplayRelation1 = new SgChecklistDisplayRelation();
//                sgChecklistDisplayRelation1.setId(IdLeaf.getId(AreaIdConstant.sg_checklist_display_relation));
                sgChecklistDisplayRelation1.setSgDisplayAreaId(sgDisplayAreaResp.getId());
                sgChecklistDisplayRelation1.setSgChecklistId(requestData.getId());
                sgChecklistDisplayRelation1.setUpdateTime(new Date());
                sgChecklistDisplayRelation1.setCreateTime(new Date());
                sgChecklistDisplayRelation1.setIsDel(IsDeleteEnum.NORMAL.getCode());
                sgChecklistDisplayRelationMapper.insert(sgChecklistDisplayRelation1);
            }
        }
    }

    private List<SgChecklistResp> buildSgChecklistResp(List<SgChecklist> list) {
        List<SgChecklistResp> sgChecklistResps = new ArrayList<>();
        if(CollectionUtils.isEmpty(list)){
            return sgChecklistResps;
        }
        List<Long> sgChecklistIds = list.stream().map(r -> r.getId()).collect(Collectors.toList());
        // 查询数据
        QueryWrapper<SgChecklistDisplayRelation> queryWrapper = new QueryWrapper();
        queryWrapper.in("sg_checklist_id",sgChecklistIds);
        queryWrapper.eq("is_del",IsDeleteEnum.NORMAL.getCode());
        // 查询到数据
        // 分组

        Map<Long, List<SgChecklistDisplayRelation>> groupByChecklistId  = new HashMap<>();
        List<SgDisplayAreaResp> sgDisplayAreaResps = new ArrayList<>();
        List<SgChecklistDisplayRelation> sgChecklistDisplayRelations = sgChecklistDisplayRelationMapper.selectList(queryWrapper);
        if(CollectionUtils.isNotEmpty(sgChecklistDisplayRelations)){
            groupByChecklistId = sgChecklistDisplayRelations.stream().collect(Collectors.groupingBy(r -> r.getSgChecklistId()));
            List<Long> areaIds = sgChecklistDisplayRelations.stream().filter(r -> r.getSgDisplayAreaId() != null).map(r -> r.getSgDisplayAreaId()).collect(Collectors.toList());
            // 批量查询
            QueryWrapper<SgDisplayArea> queryWrapper1 = new QueryWrapper();
            queryWrapper1.in("id",areaIds);
            queryWrapper1.eq("is_del",IsDeleteEnum.NORMAL.getCode());
            List<SgDisplayArea> sgDisplayAreas = sgDisplayAreaMapper.selectList(queryWrapper1);
            sgDisplayAreaResps = sgDisplayAreaService.buildSgDisplayAreaResp(sgDisplayAreas);
        }
        // 根据 陈列区域id分组
        Map<Long, List<SgDisplayAreaResp>> groupByAreaId = sgDisplayAreaResps.stream().collect(Collectors.groupingBy(r -> r.getId()));

        for (SgChecklist sgChecklist : list) {
            SgChecklistResp sgChecklistResp = new SgChecklistResp();
            BeanUtils.copyProperties(sgChecklist,sgChecklistResp);
            List<SgChecklistDisplayRelation> sgChecklistDisplayRelations1 = groupByChecklistId.get(sgChecklist.getId());
            if(CollectionUtils.isNotEmpty(sgChecklistDisplayRelations1)){
                List<SgDisplayAreaResp> resultSgDisplayAreaResp = new ArrayList<>();
                for (SgChecklistDisplayRelation sgChecklistDisplayRelation : sgChecklistDisplayRelations1) {
                    Long sgDisplayAreaId = sgChecklistDisplayRelation.getSgDisplayAreaId();
                    List<SgDisplayAreaResp> sgDisplayAreaResps1 = groupByAreaId.get(sgDisplayAreaId);
                    if(CollectionUtils.isNotEmpty(sgDisplayAreaResps1)){
                        resultSgDisplayAreaResp.addAll(sgDisplayAreaResps1);
                    }
                }
                sgChecklistResp.setSgDisplayAreaRespList(resultSgDisplayAreaResp);
            }
            sgChecklistResps.add(sgChecklistResp);
        }
        
        return sgChecklistResps;
    }
}
