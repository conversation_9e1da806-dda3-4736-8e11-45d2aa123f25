package org.springcenter.store.service;

import com.jnby.common.Page;
import org.springcenter.store.dto.*;
import org.springcenter.store.entity.SgStoreChecklistAnswer;

import java.util.List;

public interface SgStoreChecklistAnswerService {
    /**
     * 根据  检查表id和 门店id查询已经提交的数据
     * @param requestData
     * @param page
     * @return
     */
    List<SgStoreChecklistAnswerDto> getSubmitStoreChecklistAnswer(SubmitStoreChecklistAnswerReq requestData, Page page);

    /**
     * 提交数据
     * @param requestData
     * @param page
     */
    void submit(List<SgStoreChecklistAnswerDto> requestData, Page page);


    /**
     * 封装数据
     * @param sgStoreChecklistAnswers
     * @return
     */
    public List<SgStoreChecklistAnswerDto> buildSgStoreChecklistAnswerDto(List<SgStoreChecklistAnswer> sgStoreChecklistAnswers);

    /**
     * 后台列表信息
     * @param requestData
     * @param page
     * @return
     */
    List<SgStoreChecklistAnswerDto> list(ListStoreChecklistDto requestData, Page page);

    /**
     * 审批
     * @param requestData
     * @param page
     */
    void audit(SgStoreChecklistAnswerDto requestData, Page page);

    /**
     * 整改信息 展示
     * @param requestData
     * @param page
     * @return
     */
    CorrectionInfoDto correctionInfo(String requestData, Page page);

    /**
     * 生成整改任务
     * @param requestData
     * @param page
     */
    void createTask(CreateTaskDto requestData, Page page);
}
