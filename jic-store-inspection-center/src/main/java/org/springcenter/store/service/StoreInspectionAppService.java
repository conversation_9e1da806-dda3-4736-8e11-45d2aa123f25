package org.springcenter.store.service;

import org.springcenter.store.dto.*;

import com.jnby.common.Page;

import java.util.List;

public interface StoreInspectionAppService {
    /**
     * 查询是否为检查员
     */
    SiInspectorCheckResp checkInspector(SiInspectorCheckReq req);

    /**
     * 查询检查表名称
     */
    List<SiChecklistNameResp> listTemplateName(SiChecklistReq req);

    /**
     * 加载检查表
     */
    SiChecklistLoadResp loadTemplate(SiChecklistLoadReq req);

    /**
     * 保存报告
     */
    SiReportSaveResp saveReport(SiReportSaveReq req);

    /**
     * 分页拉取整改任务门店
     */
    List<SiPageStoreListResp> listStore(SiPageStoreListReq req, Page page);

} 