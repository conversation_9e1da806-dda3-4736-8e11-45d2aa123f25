package org.springcenter.store.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.jnby.common.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springcenter.store.config.exception.BizException;
import org.springcenter.store.convert.StoreInspectionConvert;
import org.springcenter.store.dto.SiTemplateDetailResp;
import org.springcenter.store.dto.SiTemplateListReq;
import org.springcenter.store.dto.SiTemplateResp;
import org.springcenter.store.dto.SiTemplateSaveReq;
import org.springcenter.store.entity.StoreInspectionTemplate;
import org.springcenter.store.entity.StoreInspectionTemplateDetail;
import org.springcenter.store.mapper.StoreInspectionTemplateMapper;
import org.springcenter.store.service.StoreInspectionTemplateDetailService;
import org.springcenter.store.service.StoreInspectionTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class StoreInspectionTemplateServiceImpl extends ServiceImpl<StoreInspectionTemplateMapper, StoreInspectionTemplate> implements StoreInspectionTemplateService {

    @Resource
    private StoreInspectionTemplateDetailService templateDetailService;
    @Resource
    private StoreInspectionTemplateMapper templateMapper;

    @Autowired
    @Qualifier("storeTransactionTemplate")
    private TransactionTemplate transactionTemplate;

    @Override
    public Long saveTemplate(SiTemplateSaveReq req) {
        // 校验模板名称是否重复
        LambdaQueryWrapper<StoreInspectionTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreInspectionTemplate::getTitle, req.getTitle())
                .eq(StoreInspectionTemplate::getIsDelete, false);
        if (this.count(queryWrapper) > 0) {
            throw new BizException("模板名称已存在");
        }

        // 使用MapStruct转换实体
        StoreInspectionTemplate template = StoreInspectionConvert.INSTANCE.toTemplate(req);
        template.setCreatePerson(req.getOptPerson());
        template.setCreateTime(LocalDateTime.now());
        template.setUpdatePerson(req.getOptPerson());
        template.setUpdateTime(LocalDateTime.now());

        final List<StoreInspectionTemplateDetail> detailList = req.getCategoryList() != null && !req.getCategoryList().isEmpty()
                ? StoreInspectionConvert.INSTANCE.toTemplateDetailList(req.getCategoryList())
                : new ArrayList<>();

        if (CollectionUtils.isEmpty(detailList)) {
            throw new BizException("模板中未选择检查项，请检查后重试。");
        }

        // 使用TransactionTemplate执行事务
        return transactionTemplate.execute(status -> {
            try {
                // 保存模板
                this.save(template);

                // 保存模板明细
                for (StoreInspectionTemplateDetail detail : detailList) {
                    detail.setTemplateId(template.getId());
                    detail.setCreatePerson(req.getOptPerson());
                    detail.setCreateTime(LocalDateTime.now());
                    detail.setUpdatePerson(req.getOptPerson());
                    detail.setUpdateTime(LocalDateTime.now());
                }
                templateDetailService.saveBatch(detailList);

                return template.getId();
            } catch (Exception e) {
                status.setRollbackOnly();
                throw e;
            }
        });
    }

    @Override
    public SiTemplateResp getTemplateDetail(Long id) {
        // 查询模板
        StoreInspectionTemplate template = this.getById(id);
        if (template == null || template.getIsDelete()) {
            throw new BizException("模板不存在");
        }

        // 查询模板明细
        LambdaQueryWrapper<StoreInspectionTemplateDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreInspectionTemplateDetail::getTemplateId, id)
                .eq(StoreInspectionTemplateDetail::getIsDelete, false);
        List<StoreInspectionTemplateDetail> detailList = templateDetailService.list(queryWrapper);

        // 转换响应
        SiTemplateResp resp = StoreInspectionConvert.INSTANCE.toTemplateResp(template);
        List<SiTemplateDetailResp> detailRespList = StoreInspectionConvert.INSTANCE.toTemplateDetailRespList(detailList);

        // 按分类分组，组装2级结构
        Map<String, List<SiTemplateDetailResp>> detailMap = detailRespList.stream()
                .collect(Collectors.groupingBy(SiTemplateDetailResp::getCheckCategoryId));
        List<org.springcenter.store.dto.SiTemplateCategorySaveReq> categoryList = new ArrayList<>();
        for (Map.Entry<String, List<SiTemplateDetailResp>> entry : detailMap.entrySet()) {
            org.springcenter.store.dto.SiTemplateCategorySaveReq category = new org.springcenter.store.dto.SiTemplateCategorySaveReq();
            category.setCheckCategoryId(entry.getKey());
            if (!entry.getValue().isEmpty()) {
                category.setCheckCategoryTitle(entry.getValue().get(0).getCheckCategoryTitle());
            }
            // 组装明细
            List<org.springcenter.store.dto.SiTemplateDetailSaveReq> detailSaves = new ArrayList<>();
            for (SiTemplateDetailResp detailResp : entry.getValue()) {
                org.springcenter.store.dto.SiTemplateDetailSaveReq detailSave = new org.springcenter.store.dto.SiTemplateDetailSaveReq();
                detailSave.setCheckDetailId(detailResp.getCheckDetailId());
                detailSave.setCheckDetailTitle(detailResp.getCheckDetailTitle());
                detailSave.setCheckDetailStandard(detailResp.getCheckDetailStandard());
                detailSave.setScore(detailResp.getScore());
                detailSaves.add(detailSave);
            }
            category.setDetailList(detailSaves);
            categoryList.add(category);
        }
        // 设置统计信息
        resp.setCategoryCount(categoryList.size());
        resp.setDetailCount(detailRespList.size());
        resp.setCategoryList(categoryList);
        return resp;
    }

    @Override
    public Long updateTemplate(Long id, SiTemplateSaveReq req) {
        // 校验模板是否存在
        StoreInspectionTemplate template = this.getById(id);
        if (template == null) {
            throw new BizException("模板不存在");
        }

        // 校验模板名称是否重复
        LambdaQueryWrapper<StoreInspectionTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreInspectionTemplate::getTitle, req.getTitle())
                .eq(StoreInspectionTemplate::getIsDelete, false)
                .ne(StoreInspectionTemplate::getId, id);
        if (this.count(queryWrapper) > 0) {
            throw new BizException("模板名称已存在");
        }

        // 使用MapStruct转换实体
        final StoreInspectionTemplate updateTemplate = StoreInspectionConvert.INSTANCE.toTemplate(req);
        updateTemplate.setId(id);
        final List<StoreInspectionTemplateDetail> detailList = req.getCategoryList() != null && !req.getCategoryList().isEmpty()
                ? StoreInspectionConvert.INSTANCE.toTemplateDetailList(req.getCategoryList())
                : new ArrayList<>();

        if (CollectionUtils.isEmpty(detailList)) {
            throw new BizException("模板中未选择检查项，请检查后重试。");
        }
        StoreInspectionTemplateDetail updateDetail = new StoreInspectionTemplateDetail();
        updateDetail.setIsDelete(true);
        updateDetail.setUpdatePerson(req.getOptPerson());
        updateDetail.setUpdateTime(LocalDateTime.now());
        LambdaQueryWrapper<StoreInspectionTemplateDetail> detailQuery = new LambdaQueryWrapper<>();
        detailQuery.eq(StoreInspectionTemplateDetail::getTemplateId, id).eq(StoreInspectionTemplateDetail::getIsDelete, false);

        // 使用TransactionTemplate执行事务
        return transactionTemplate.execute(status -> {
            try {
                // 更新模板
                updateTemplate.setUpdatePerson(req.getOptPerson());
                updateTemplate.setUpdateTime(LocalDateTime.now());
                this.updateById(updateTemplate);

                // 逻辑删除原模板明细
                templateDetailService.update(updateDetail, detailQuery);

                // 保存新模板明细
                for (StoreInspectionTemplateDetail detail : detailList) {
                    detail.setTemplateId(id);
                    detail.setCreatePerson(req.getOptPerson());
                    detail.setCreateTime(LocalDateTime.now());
                    detail.setUpdatePerson(req.getOptPerson());
                    detail.setUpdateTime(LocalDateTime.now());
                }
                templateDetailService.saveBatch(detailList);

                return id;
            } catch (Exception e) {
                status.setRollbackOnly();
                throw e;
            }
        });
    }

    @Override
    public List<SiTemplateResp> listTemplate(Page page, SiTemplateListReq req) {
        // 构建查询条件
        LambdaQueryWrapper<StoreInspectionTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreInspectionTemplate::getIsDelete, false)
                .like(StringUtils.hasText(req.getTitle()), StoreInspectionTemplate::getTitle, req.getTitle())
                .eq(req.getCanUse() != null, StoreInspectionTemplate::getCanUse, req.getCanUse())
                .orderByDesc(StoreInspectionTemplate::getUpdateTime);

        // 查询模板列表
        com.github.pagehelper.Page<StoreInspectionTemplate> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        templateMapper.selectList(queryWrapper);
        PageInfo<StoreInspectionTemplate> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<StoreInspectionTemplate> templateList = pageInfo.getList();
        if (CollectionUtils.isEmpty(templateList)) {
            return new ArrayList<>();
        }

        // 查询模板明细
        List<Long> templateIds = templateList.stream().map(StoreInspectionTemplate::getId).collect(Collectors.toList());
        LambdaQueryWrapper<StoreInspectionTemplateDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper
                .in(StoreInspectionTemplateDetail::getTemplateId, templateIds)
                .eq(StoreInspectionTemplateDetail::getIsDelete, false);
        List<StoreInspectionTemplateDetail> detailList = templateDetailService.list(detailWrapper);

        // 按模板ID分组
        List<SiTemplateDetailResp> list = StoreInspectionConvert.INSTANCE.toTemplateDetailRespList(detailList);
        Map<Long, List<SiTemplateDetailResp>> detailMap = list.stream()
                .collect(Collectors.groupingBy(SiTemplateDetailResp::getTemplateId));

        // 转换响应
        List<SiTemplateResp> respList = templateList.stream().map(template -> {
            SiTemplateResp resp = StoreInspectionConvert.INSTANCE.toTemplateResp(template);
            List<SiTemplateDetailResp> templateDetails = detailMap.getOrDefault(template.getId(), new ArrayList<>());
            // 组装2级结构
            Map<String, List<SiTemplateDetailResp>> categoryMap = templateDetails.stream()
                    .collect(Collectors.groupingBy(SiTemplateDetailResp::getCheckCategoryId));
            List<org.springcenter.store.dto.SiTemplateCategorySaveReq> categoryList = new ArrayList<>();
            for (Map.Entry<String, List<SiTemplateDetailResp>> entry : categoryMap.entrySet()) {
                org.springcenter.store.dto.SiTemplateCategorySaveReq category = new org.springcenter.store.dto.SiTemplateCategorySaveReq();
                category.setCheckCategoryId(entry.getKey());
                if (!entry.getValue().isEmpty()) {
                    category.setCheckCategoryTitle(entry.getValue().get(0).getCheckCategoryTitle());
                }
                List<org.springcenter.store.dto.SiTemplateDetailSaveReq> detailSaves = new ArrayList<>();
                for (SiTemplateDetailResp detailResp : entry.getValue()) {
                    org.springcenter.store.dto.SiTemplateDetailSaveReq detailSave = new org.springcenter.store.dto.SiTemplateDetailSaveReq();
                    detailSave.setCheckDetailId(detailResp.getCheckDetailId());
                    detailSave.setCheckDetailTitle(detailResp.getCheckDetailTitle());
                    detailSave.setCheckDetailStandard(detailResp.getCheckDetailStandard());
                    detailSave.setScore(detailResp.getScore());
                    detailSaves.add(detailSave);
                }
                category.setDetailList(detailSaves);
                categoryList.add(category);
            }
            // 设置统计信息
            resp.setCategoryCount(categoryList.size());
            resp.setDetailCount(templateDetails.size());
            resp.setCategoryList(categoryList);
            return resp;
        }).collect(Collectors.toList());

        // 设置分页信息
        return respList;
    }

    @Override
    public void enableTemplate(Long id, String operator) {
        Preconditions.checkNotNull(id, "模板ID不能为空");
        StoreInspectionTemplate template = this.getById(id);
        if (template == null || template.getIsDelete()) {
            throw new BizException("模板不存在");
        }
        StoreInspectionTemplate update = new StoreInspectionTemplate();
        update.setId(id);
        update.setCanUse(true);
        update.setUpdatePerson(operator);
        update.setUpdateTime(LocalDateTime.now());
        this.updateById(update);
    }

    @Override
    public void disableTemplate(Long id, String operator) {
        Preconditions.checkNotNull(id, "模板ID不能为空");
        StoreInspectionTemplate template = this.getById(id);
        if (template == null || template.getIsDelete()) {
            throw new BizException("模板不存在");
        }
        StoreInspectionTemplate update = new StoreInspectionTemplate();
        update.setId(id);
        update.setCanUse(false);
        update.setUpdatePerson(operator);
        update.setUpdateTime(LocalDateTime.now());
        this.updateById(update);
    }
} 