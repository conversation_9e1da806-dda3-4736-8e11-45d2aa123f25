package org.springcenter.store.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springcenter.store.config.exception.BizException;
import org.springcenter.store.convert.StoreInspectionConvert;
import org.springcenter.store.dto.SiChecklistListReq;
import org.springcenter.store.dto.SiChecklistResp;
import org.springcenter.store.dto.SiChecklistSaveReq;
import org.springcenter.store.dto.SiChecklistSaveReq.Inspector;
import org.springcenter.store.dto.SiTemplateResp;
import org.springcenter.store.entity.StoreInspectionChecklist;
import org.springcenter.store.entity.StoreInspectionPerson;
import org.springcenter.store.entity.StoreInspectionTemplate;
import org.springcenter.store.mapper.StoreInspectionChecklistMapper;
import org.springcenter.store.mapper.StoreInspectionTemplateMapper;
import org.springcenter.store.service.StoreInspectionChecklistService;
import org.springcenter.store.service.StoreInspectionPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StoreInspectionChecklistServiceImpl extends ServiceImpl<StoreInspectionChecklistMapper, StoreInspectionChecklist> implements StoreInspectionChecklistService {
    @Resource
    private StoreInspectionChecklistMapper checklistMapper;
    @Resource
    private StoreInspectionTemplateMapper templateMapper;
    @Resource
    private StoreInspectionPersonService personService;

    @Autowired
    @Qualifier("storeTransactionTemplate")
    private TransactionTemplate transactionTemplate;

    @Override
    public List<SiTemplateResp> getEnabledTemplates() {
        LambdaQueryWrapper<StoreInspectionTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreInspectionTemplate::getCanUse, true)
                .eq(StoreInspectionTemplate::getIsDelete, false);
        List<StoreInspectionTemplate> templates = templateMapper.selectList(wrapper);
        return templates.stream()
                .map(StoreInspectionConvert.INSTANCE::toTemplateResp)
                .collect(Collectors.toList());
    }

    @Override
    public Long saveChecklist(SiChecklistSaveReq req) {
        // 校验名称唯一性
        LambdaQueryWrapper<StoreInspectionChecklist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreInspectionChecklist::getTitle, req.getTitle())
                .eq(StoreInspectionChecklist::getIsDelete, false);
        if (checklistMapper.selectCount(wrapper) > 0) {
            throw new RuntimeException("检查表名称已存在");
        }

        // 保存检查表
        StoreInspectionChecklist checklist = StoreInspectionConvert.INSTANCE.checklist2Entity(req);
        checklist.setIsDelete(false);
        checklist.setCreatePerson(req.getOptPerson());
        checklist.setCreateTime(LocalDateTime.now());
        checklist.setUpdatePerson(req.getOptPerson());
        checklist.setUpdateTime(LocalDateTime.now());
        return transactionTemplate.execute(status -> {
            checklistMapper.insert(checklist);

            // 保存检查人员
            savePerson(req, checklist);

            return checklist.getId();
        });
    }

    private void savePerson(SiChecklistSaveReq req, StoreInspectionChecklist checklist) {
        List<StoreInspectionPerson> personList = Lists.newArrayList();
        for (Inspector inspector : req.getInspectorList()) {
            StoreInspectionPerson person = StoreInspectionConvert.INSTANCE.person2Entity(checklist, inspector);
            person.setCreatePerson(req.getOptPerson());
            person.setCreateTime(LocalDateTime.now());
            person.setUpdatePerson(req.getOptPerson());
            person.setUpdateTime(LocalDateTime.now());
            person.setIsDelete(false);
            personList.add(person);
        }
        personService.saveBatch(personList);
    }

    @Override
    public SiChecklistResp getChecklistDetail(Long id) {
        // 获取检查表信息
        LambdaQueryWrapper<StoreInspectionChecklist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreInspectionChecklist::getId, id)
                .eq(StoreInspectionChecklist::getIsDelete, false);
        StoreInspectionChecklist checklist = checklistMapper.selectOne(wrapper);
        if (checklist == null) {
            throw new BizException("检查表不存在");
        }

        // 获取检查人员信息
        LambdaQueryWrapper<StoreInspectionPerson> personQuery = new LambdaQueryWrapper<>();
        personQuery.eq(StoreInspectionPerson::getChecklistId, id)
                .eq(StoreInspectionPerson::getIsDelete, false);
        List<StoreInspectionPerson> personList = personService.list(personQuery);

        // 组装返回数据
        SiChecklistResp resp = StoreInspectionConvert.INSTANCE.checklist2Resp(checklist);
        // 组装inspectorList
        List<SiChecklistResp.Inspector> inspectors = personList.stream().map(person -> {
            SiChecklistResp.Inspector inspector = new SiChecklistResp.Inspector();
            inspector.setUserId(person.getUserId());
            inspector.setUserName(person.getUserName());
            inspector.setOutId(person.getOutId());
            return inspector;
        }).collect(Collectors.toList());
        resp.setInspectorList(inspectors);

        return resp;
    }

    @Override
    public Long updateChecklist(Long id, SiChecklistSaveReq req) {
        // 校验检查表是否存在
        LambdaQueryWrapper<StoreInspectionChecklist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreInspectionChecklist::getId, id)
                .eq(StoreInspectionChecklist::getIsDelete, false);
        StoreInspectionChecklist checklist = checklistMapper.selectOne(wrapper);
        if (checklist == null) {
            throw new RuntimeException("检查表不存在");
        }

        // 校验名称唯一性
        if (!checklist.getTitle().equals(req.getTitle())) {
            LambdaQueryWrapper<StoreInspectionChecklist> nameWrapper = new LambdaQueryWrapper<>();
            nameWrapper.eq(StoreInspectionChecklist::getTitle, req.getTitle())
                    .eq(StoreInspectionChecklist::getIsDelete, false);
            if (checklistMapper.selectCount(nameWrapper) > 0) {
                throw new RuntimeException("检查表名称已存在");
            }
        }

        return transactionTemplate.execute(status -> {
            // 更新检查表
            StoreInspectionChecklist updateChecklist = StoreInspectionConvert.INSTANCE.checklist2Entity(req);
            updateChecklist.setId(id);
            updateChecklist.setUpdatePerson(req.getOptPerson());
            updateChecklist.setUpdateTime(LocalDateTime.now());
            checklistMapper.updateById(updateChecklist);

            // 逻辑删除原检查人员
            StoreInspectionPerson updatePerson = new StoreInspectionPerson();
            updatePerson.setIsDelete(true);
            updatePerson.setUpdatePerson(req.getOptPerson());
            updatePerson.setUpdateTime(LocalDateTime.now());
            LambdaQueryWrapper<StoreInspectionPerson> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(StoreInspectionPerson::getChecklistId, id);
            deleteWrapper.eq(StoreInspectionPerson::getIsDelete, false);
            personService.update(updatePerson, deleteWrapper);

            // 保存新检查人员
            savePerson(req, updateChecklist);

            return id;
        });
    }

    @Override
    public void enableChecklist(Long id, String operator) {
        StoreInspectionChecklist checklist = new StoreInspectionChecklist();
        checklist.setId(id);
        checklist.setCanUse(true);
        checklist.setUpdatePerson(operator);
        checklist.setUpdateTime(LocalDateTime.now());
        checklistMapper.updateById(checklist);
    }

    @Override
    public void disableChecklist(Long id, String operator) {
        StoreInspectionChecklist checklist = new StoreInspectionChecklist();
        checklist.setId(id);
        checklist.setCanUse(false);
        checklist.setUpdatePerson(operator);
        checklist.setUpdateTime(LocalDateTime.now());
        checklistMapper.updateById(checklist);
    }

    @Override
    public List<SiChecklistResp> listChecklist(Page page, SiChecklistListReq req) {
        // 构建查询条件
        LambdaQueryWrapper<StoreInspectionChecklist> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(req.getName() != null, StoreInspectionChecklist::getTitle, req.getName())
                .eq(req.getEnabled() != null, StoreInspectionChecklist::getCanUse, req.getEnabled())
                .eq(StoreInspectionChecklist::getIsDelete, false)
                .orderByDesc(StoreInspectionChecklist::getUpdateTime);

        // 查询数据
        com.github.pagehelper.Page<StoreInspectionChecklist> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<StoreInspectionChecklist> checklists = checklistMapper.selectList(wrapper);
        PageInfo<StoreInspectionChecklist> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<StoreInspectionChecklist> templateList = pageInfo.getList();
        if (CollectionUtils.isEmpty(templateList)) {
            return new ArrayList<>();
        }

        // TODO 汇总使用次数

        // 转换为响应对象
        return checklists.stream()
                .map(StoreInspectionConvert.INSTANCE::checklist2Resp)
                .collect(Collectors.toList());
    }
} 