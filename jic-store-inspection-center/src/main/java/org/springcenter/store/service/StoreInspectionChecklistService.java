package org.springcenter.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jnby.common.Page;
import org.springcenter.store.dto.SiChecklistListReq;
import org.springcenter.store.dto.SiChecklistResp;
import org.springcenter.store.dto.SiChecklistSaveReq;
import org.springcenter.store.dto.SiTemplateResp;
import org.springcenter.store.entity.StoreInspectionChecklist;
import org.springcenter.store.entity.StoreInspectionTemplate;

import java.util.List;

public interface StoreInspectionChecklistService extends IService<StoreInspectionChecklist> {
    
    /**
     * 获取启用的模板列表
     */
    List<SiTemplateResp> getEnabledTemplates();
    
    /**
     * 保存检查表
     */
    Long saveChecklist(SiChecklistSaveReq req);
    
    /**
     * 获取检查表详情
     */
    SiChecklistResp getChecklistDetail(Long id);
    
    /**
     * 更新检查表
     */
    Long updateChecklist(Long id, SiChecklistSaveReq req);
    
    /**
     * 启用检查表
     */
    void enableChecklist(Long id, String operator);
    
    /**
     * 禁用检查表
     */
    void disableChecklist(Long id, String operator);
    
    /**
     * 查询检查表列表
     */
    List<SiChecklistResp> listChecklist(Page page, SiChecklistListReq req);
} 