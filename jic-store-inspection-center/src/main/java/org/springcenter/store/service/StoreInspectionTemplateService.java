package org.springcenter.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jnby.common.Page;
import org.springcenter.store.dto.SiTemplateListReq;
import org.springcenter.store.dto.SiTemplateResp;
import org.springcenter.store.dto.SiTemplateSaveReq;
import org.springcenter.store.entity.StoreInspectionTemplate;

import java.util.List;

public interface StoreInspectionTemplateService extends IService<StoreInspectionTemplate> {

    /**
     * 保存模板
     *
     * @param req 保存请求
     * @return 模板ID
     */
    Long saveTemplate(SiTemplateSaveReq req);

    /**
     * 查看模板详情
     *
     * @param id 模板ID
     * @return 模板详情
     */
    SiTemplateResp getTemplateDetail(Long id);

    /**
     * 编辑模板
     *
     * @param id  模板ID
     * @param req 保存请求
     * @return 模板ID
     */
    Long updateTemplate(Long id, SiTemplateSaveReq req);

    /**
     * 查询模板列表
     *
     * @param page 分页参数
     * @param req  查询条件
     * @return 模板列表
     */
    List<SiTemplateResp> listTemplate(Page page, SiTemplateListReq req);

    /**
     * 启用模板
     * @param id 模板id
     * @param operator 操作人
     */
    void enableTemplate(Long id, String operator);

    /**
     * 禁用模板
     * @param id 模板id
     * @param operator 操作人
     */
    void disableTemplate(Long id, String operator);
} 