package org.springcenter.store.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("整改任务列表响应")
public class SiTaskListResp {
    @ApiModelProperty("任务ID")
    private Long id;
    
    @ApiModelProperty("门店ID")
    private Long storeId;
    
    @ApiModelProperty("门店名称")
    private String storeName;
    
    @ApiModelProperty("门店编号")
    private String storeCode;
    
    @ApiModelProperty("分类名称")
    private String checkCategoryTitle;
    
    @ApiModelProperty("检查项名称")
    private String checkDetailTitle;
    
    @ApiModelProperty("整改内容")
    private String adjustContent;
    
    @ApiModelProperty("整改任务状态")
    private Integer status;
    
    @ApiModelProperty("整改任务状态描述")
    private String statusDesc;
    
    @ApiModelProperty("整改截止日期")
    private LocalDateTime deadline;
    
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}