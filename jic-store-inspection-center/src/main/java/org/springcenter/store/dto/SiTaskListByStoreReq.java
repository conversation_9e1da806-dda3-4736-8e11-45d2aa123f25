package org.springcenter.store.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("根据门店查询整改任务请求")
public class SiTaskListByStoreReq {
    
    @ApiModelProperty(value = "门店ID", required = true)
    @NotNull(message = "门店ID不能为空")
    private Long storeId;
    
    @ApiModelProperty("反馈状态（支持多个状态组合查询）")
    private List<Integer> statusList;
    
    @ApiModelProperty("排序模式（1-任务创建时间倒序，2-任务更新时间倒序）")
    private Integer sortMode = 1;
}