package org.springcenter.store.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SgChecklistResp {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 检查表名称
     */
    @ApiModelProperty(value = "检查表名称")
    private String name;

    /**
     * 描述内容
     */
    @ApiModelProperty(value = "描述内容")
    private String content;

    /**
     * 检查数量  对应陈列区域数字
     */
    @ApiModelProperty(value = "检查数量  对应陈列区域数字")
    private Integer checkNum;

    /**
     * 0 禁用  1 启用
     */
    @ApiModelProperty(value = "0 禁用  1 启用")
    private Integer status;

    /**
     * 0 正常  1 删除
     */
    @ApiModelProperty(value = "0 正常  1 删除")
    private Integer isDel;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    private String linkUrl;


    @ApiModelProperty(value = "陈列区域信息  检查数量  对应陈列区域数字  新增或者编辑的时候  仅需要传id即可" )
    private List<SgDisplayAreaResp> sgDisplayAreaRespList;


}
