<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.store.mapper.StoreInspectionAdjustTaskRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.store.entity.StoreInspectionAdjustTaskRecord">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="create_person" property="createPerson"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_person" property="updatePerson"/>
        <result column="is_delete" property="isDelete"/>
        <result column="adjust_task_id" property="adjustTaskId"/>
        <result column="type" property="type"/>
        <result column="pic_list" property="picList"/>
        <result column="description" property="description"/>
        <result column="result" property="result"/>
        <result column="from_status" property="fromStatus"/>
        <result column="to_status" property="toStatus"/>
    </resultMap>

</mapper> 