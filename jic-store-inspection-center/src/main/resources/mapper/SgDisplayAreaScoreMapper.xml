<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.store.mapper.SgDisplayAreaScoreMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.store.entity.SgDisplayAreaScore">
            <id property="id" column="id"/>
            <result property="sgDisplayAreaId" column="sg_display_area_id"/>
            <result property="requireContent" column="require_content" jdbcType="VARCHAR"/>
            <result property="score" column="score" jdbcType="INTEGER"/>
            <result property="isDel" column="is_del" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sg_display_area_id,require_content,
        score,is_del,create_by,
        create_time,update_time
    </sql>
    <select id="selectByDisplayAreaIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from sg_display_area_score
        where is_del = 0 and  sg_display_area_id in
                              <foreach collection="displayAreaIds" open="(" close=")" item="item" separator=",">
                                  #{item}
                              </foreach>
    </select>
</mapper>
