<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.store.mapper.StoreInspectionChecklistMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.store.entity.StoreInspectionChecklist">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="create_person" property="createPerson"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_person" property="updatePerson"/>
        <result column="is_delete" property="isDelete"/>
        <result column="title" property="title"/>
        <result column="description" property="description"/>
        <result column="can_use" property="canUse"/>
        <result column="store_pkg_id" property="storePkgId"/>
        <result column="feedback_method" property="feedbackMethod"/>
        <result column="template_id" property="templateId"/>
    </resultMap>

</mapper> 