<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.store.mapper.SgStoreChecklistAnswerScoreMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.store.entity.SgStoreChecklistAnswerScore">
            <id property="id" column="id" />
            <result property="sgDisplayAreaScoreId" column="sg_display_area_score_id" />
            <result property="sgStoreChecklistAnswerId" column="sg_store_checklist_answer_id"/>
            <result property="requireContent" column="require_content" jdbcType="VARCHAR"/>
            <result property="score" column="score" jdbcType="INTEGER"/>
            <result property="commentScore" column="comment_score" jdbcType="INTEGER"/>
            <result property="isDel" column="is_del" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sg_display_area_score_id,sg_store_checklist_answer_id,
        require_content,score,comment_score,
        is_del,create_time,update_time
    </sql>
</mapper>
