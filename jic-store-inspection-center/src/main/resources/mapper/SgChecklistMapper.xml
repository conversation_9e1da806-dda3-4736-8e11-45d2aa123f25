<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.store.mapper.SgChecklistMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.store.entity.SgChecklist">
            <id property="id" column="id" />
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDel" column="is_del" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="linkUrl" column="link_url" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,content,
        status,is_del,
        create_by,create_time,update_time,
        link_url
    </sql>
    <select id="selectListByReq" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from     sg_checklist
        where is_del = 0
        <if test="checklistReq.ids != null and checklistReq.ids.size() > 0">
            and id in
            <foreach collection="checklistReq.ids" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="checklistReq.name != null and checklistReq.name != '' ">
            and name like concat('%',concat(#{checklistReq.name},'%'))
        </if>
        <if test="checklistReq.status != null">
            and status  = #{checklistReq.status}
        </if>
        order by update_time desc
    </select>
</mapper>
