<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.store.mapper.SgChecklistDisplayRelationMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.store.entity.SgChecklistDisplayRelation">
            <result property="id" column="id"/>
            <result property="sgChecklistId" column="sg_checklist_id" />
            <result property="sgDisplayAreaId" column="sg_display_area_id"/>
            <result property="isDel" column="is_del" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sg_checklist_id,sg_display_area_id,
        is_del,create_time,update_time
    </sql>
</mapper>
