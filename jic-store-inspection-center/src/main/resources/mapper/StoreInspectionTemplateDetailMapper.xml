<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.store.mapper.StoreInspectionTemplateDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.store.entity.StoreInspectionTemplateDetail">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="create_person" property="createPerson"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_person" property="updatePerson"/>
        <result column="is_delete" property="isDelete"/>
        <result column="template_id" property="templateId"/>
        <result column="check_category_id" property="checkCategoryId"/>
        <result column="check_category_title" property="checkCategoryTitle"/>
        <result column="check_detail_id" property="checkDetailId"/>
        <result column="check_detail_title" property="checkDetailTitle"/>
        <result column="check_detail_standard" property="checkDetailStandard"/>
        <result column="score" property="score"/>
    </resultMap>

</mapper> 