<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.store.mapper.SgStoreChecklistAnswerMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.store.entity.SgStoreChecklistAnswer">
            <result property="id" column="id" />
            <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
            <result property="sgDisplayAreaName" column="sg_display_area_name" jdbcType="VARCHAR"/>
            <result property="sgDisplayAreaId" column="sg_display_area_id"/>
            <result property="sgChecklistId" column="sg_checklist_id" />
            <result property="storeId" column="store_id" jdbcType="VARCHAR"/>
            <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="position" column="position" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDel" column="is_del" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="comment" column="comment" jdbcType="VARCHAR"/>
            <result property="isCorrection" column="is_correction" jdbcType="INTEGER"/>
            <result property="isCreateTask" column="is_create_task" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,task_id,sg_display_area_name,
        sg_display_area_id,sg_checklist_id,store_id,
        store_name,create_by,position,
        status,is_del,create_time,
        update_time,comment,is_correction,is_create_task
    </sql>

</mapper>
