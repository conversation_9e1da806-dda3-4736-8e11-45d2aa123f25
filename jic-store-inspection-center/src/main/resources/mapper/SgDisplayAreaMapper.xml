<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.store.mapper.SgDisplayAreaMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.store.entity.SgDisplayArea">
            <id property="id" column="id" />
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDel" column="is_del" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,content,
        status,is_del,create_by,
        create_time,update_time
    </sql>
    <select id="selectListByReq" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from sg_display_area
        <where>
            is_del = 0
            <if test="displayAreaListReq.ids != null and displayAreaListReq.ids.size() > 0">
                   and id in
                <foreach collection="displayAreaListReq.ids" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="displayAreaListReq.name != null and displayAreaListReq.name != '' ">
                and name like concat('%',concat(#{displayAreaListReq.name},'%'))
            </if>
            <if test="displayAreaListReq.status != null">
                and status  = #{displayAreaListReq.status}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>
