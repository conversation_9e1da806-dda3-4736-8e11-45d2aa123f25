<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.store.mapper.StoreInspectionReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.store.entity.StoreInspectionReport">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="create_person" property="createPerson"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_person" property="updatePerson"/>
        <result column="is_delete" property="isDelete"/>
        <result column="store_code" property="storeCode"/>
        <result column="store_name" property="storeName"/>
        <result column="total_gain_score" property="totalGainScore"/>
        <result column="gain_score_rate" property="gainScoreRate"/>
        <result column="deadline" property="deadline"/>
        <result column="summary" property="summary"/>
        <result column="checklist_id" property="checklistId"/>
        <result column="checklist_title" property="checklistTitle"/>
    </resultMap>

</mapper> 